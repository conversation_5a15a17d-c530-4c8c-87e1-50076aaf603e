// Export all tools from the lib/tools directory

// DateTime Tool
export { DateTimeTool, dateTimeTool } from './dateTimeTool';
export type { DateTimeFormatOptions } from './dateTimeTool';

// Prompt Engineering Tools
export { PromptInterpreterTool, promptInterpreterTool } from './promptInterpreterTool';
export type { PromptInterpreterOptions, PromptInterpreterResult } from './promptInterpreterTool';

export { PromptOptimizerTool, promptOptimizerTool } from './promptOptimizerTool';
export type { PromptOptimizerOptions, PromptOptimizerResult } from './promptOptimizerTool';

// Web Scraper Tool
export { WebScraperTool, webScraperTool } from './web-scraper';
export type { ScrapedContent } from './web-scraper';

// Content Formatter Tool
export { ContentFormatterTool, contentFormatterTool, processContentWithLLM } from './content-formatter';
export type { ContentFormattingOptions } from './content-formatter';

// Web Content Extractor Tool (Combined Web Scraper and Content Formatter)
export {
  WebContentExtractorTool,
  webContentExtractorTool,
  extractAndFormatContent
} from './web-content-extractor';
export type { ExtractionOptions, ExtractionResult } from './web-content-extractor';

// Academic Search Tool
export { AcademicSearchTool, academicSearchTool } from './academic-search';
export type {
  AcademicSearchResultItem,
  AcademicSearchOptions,
  AcademicSearchResult,
  AcademicSearchResultMetadata
} from './academic-search';

// LLM Tool
export { LlmTool, llmTool } from './llm-tool';
export type { LlmProvider, ModelOptions, WebSearchOptions, ProcessContentOptions, ProcessLargeContentOptions } from './llm-tool';

// Chart Tool
export { ChartTool, chartTool, CHART_TYPES } from './chart-tool';
export type { ChartType, ChartConfig, ChartGenerationOptions, ChartGenerationResult } from './chart-tool';
export type { TableColumn, TableChartConfig, ExtendedChartConfig } from './chart-types';

// Dashboard Tool
export { DashboardTool, dashboardTool, DASHBOARD_LAYOUTS } from './dashboard-tool';
export type { DashboardLayout } from './dashboard-tool';

// AI Providers
export { processWithOpenAI, getOpenAIModels } from './openai-ai';
export type { OpenAIProcessingOptions } from './openai-ai';

export { processWithClaude, getClaudeModels } from './claude-ai';
export type { ClaudeProcessingOptions } from './claude-ai';

export { processWithAnthropic, getAnthropicModels } from './anthropic-ai';
export { processWithAnthropic as processWithAnthropicAI } from './anthropic-ai';
export type { AnthropicProcessingOptions } from './anthropic-ai';

export { processWithGoogleAI, getGoogleAIModels } from './google-ai';
export type { GoogleAIProcessingOptions } from './google-ai';

export { processWithGroq, getGroqModels } from './groq-ai';
export { processWithGroq as processWithGroqAI } from './groq-ai';
export type { GroqProcessingOptions } from './groq-ai';

// Project Management Tools
export { createProjectTool, extractProjectFromStrategicAnalysis } from './createProjectTool';
export type { CreateProjectInput, CreateProjectResult } from './createProjectTool';

export { createTasksTool } from './createTasksTool';
export type { CreateTasksInput, CreateTasksResponse, TaskCreationResult } from './createTasksTool';

export { generateTasksListTool } from './generateTasksListTool';
export type { GenerateTasksInput, GenerateTasksResponse, TaskItem, TaskPriority } from './generateTasksListTool';
