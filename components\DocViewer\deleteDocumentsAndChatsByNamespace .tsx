import { adminDb, adminStorage } from 'components/firebase-admin';
import { getPineconeIndex } from 'lib/pinecone-client';

export const deleteDocumentAndChatsByNamespace = async (userId: string, namespace: string) => {
  console.log(`[DELETE DOC] Starting deletion process for namespace: ${namespace}, userId: ${userId}`);
  try {
    // 1. Delete the document metadata from Firestore (user's files collection)
    console.log(`[DELETE DOC] Deleting document metadata from Firestore`);
    const filesRef = adminDb.collection('users').doc(userId).collection('files');
    const filesQuery = filesRef.where('namespace', '==', namespace);
    const querySnapshot = await filesQuery.get();

    if (querySnapshot.empty) {
      console.log(`[DELETE DOC] No document found with namespace: ${namespace}`);
      throw new Error(`No document found with namespace: ${namespace}`);
    }

    let fileDocumentId = '';
    console.log(`[DELETE DOC] Found ${querySnapshot.docs.length} documents to delete`);

    // Loop through each file document and delete it
    for (const fileDoc of querySnapshot.docs) {
      fileDocumentId = fileDoc.id;
      console.log(`[DELETE DOC] Deleting document with ID: ${fileDocumentId}`);
      await fileDoc.ref.delete();
      console.log(`[DELETE DOC] Deleting associated chats for document: ${fileDocumentId}`);
      await deleteAssociatedChats(userId, fileDocumentId);
    }

    // 2. Delete the embeddings from Pinecone
    console.log(`[DELETE DOC] Attempting to delete vectors from Pinecone for namespace: ${namespace}`);
    try {
      await deleteVectorsFromPinecone(namespace);
      console.log(`[DELETE DOC] Successfully deleted vectors from Pinecone`);
    } catch (pineconeError) {
      console.error(`[DELETE DOC] Error deleting vectors from Pinecone, but continuing:`, pineconeError);
      // Continue with the deletion process even if Pinecone deletion fails
    }

    // 3. Delete the document chunks from Firestore (byteStoreCollection)
    console.log(`[DELETE DOC] Deleting document chunks from Firestore`);
    await deleteDocumentChunks(namespace, userId);

    // 4. Delete the file from Firebase Storage
    console.log(`[DELETE DOC] Deleting file from Firebase Storage`);
    try {
      await deleteFileFromStorage(namespace, userId);
      console.log(`[DELETE DOC] Successfully deleted file from Firebase Storage`);
    } catch (storageError) {
      console.error(`[DELETE DOC] Error deleting file from storage, but continuing:`, storageError);
      // Continue with the process even if storage deletion fails
    }

    // 5. Add a notification after the document and associated chats have been deleted
    console.log(`[DELETE DOC] Adding notification for user: ${userId}`);
    await addNotification(userId, `Document with namespace: ${namespace} has been successfully removed.`);

    console.log(`[DELETE DOC] Deletion process completed successfully`);
    return `Document and associated chats with namespace: ${namespace} have been successfully deleted.`;
  } catch (error) {
    console.error('[DELETE DOC] Error deleting document and chats by namespace:', error);
    throw new Error('Error deleting document and chats.');
  }
};

const deleteVectorsFromPinecone = async (namespace: string) => {
  try {
    console.log(`[DELETE VECTORS] Using fixed Pinecone index name: 'ikedia'`);
    console.log(`[DELETE VECTORS] Attempting to delete all vectors in namespace: ${namespace}`);

    try {
      // Get the index using our helper function
      const index = await getPineconeIndex();
      console.log(`[DELETE VECTORS] Retrieved Pinecone index`);

      // Try to delete all vectors in the namespace
      await index.namespace(namespace).deleteAll();
      console.log(`[DELETE VECTORS] Vectors in namespace '${namespace}' have been deleted from Pinecone.`);
    } catch (pineconeError) {
      console.error('[DELETE VECTORS] Error accessing Pinecone index:', pineconeError);

      // Try an alternative approach - delete by filter
      try {
        console.log('[DELETE VECTORS] Trying alternative approach - delete by filter');
        const index = await getPineconeIndex();
        await index.deleteMany({
          filter: { namespace: namespace }
        });
        console.log(`[DELETE VECTORS] Vectors deleted using filter approach`);
      } catch (filterError) {
        console.error('[DELETE VECTORS] Error with filter approach:', filterError);
        // Continue execution
      }
    }
  } catch (error) {
    console.error('[DELETE VECTORS] Error deleting vectors from Pinecone:', error);
    console.error('[DELETE VECTORS] Error details:', JSON.stringify(error, null, 2));

    // Continue execution instead of throwing
    console.log('[DELETE VECTORS] Continuing execution despite Pinecone error');
  }
};

const deleteAssociatedChats = async (userId: string, fileDocumentId: string) => {
  try {
    const chatsRef = adminDb.collection('users').doc(userId).collection('chats');
    const chatsQuery = chatsRef.where('fileDocumentId', '==', fileDocumentId);
    const chatSnapshot = await chatsQuery.get();

    if (!chatSnapshot.empty) {
      for (const chatDoc of chatSnapshot.docs) {
        await deleteAssociatedMessages(userId, chatDoc.id);
        await chatDoc.ref.delete();
      }
    }
  } catch (error) {
    console.error("Error deleting associated chats:", error);
    throw new Error("Error deleting associated chats.");
  }
};

const deleteAssociatedMessages = async (userId: string, chatId: string) => {
  try {
    const messagesRef = adminDb.collection('users').doc(userId).collection('chats').doc(chatId).collection('messages');
    const messagesSnapshot = await messagesRef.get();
    for (const messageDoc of messagesSnapshot.docs) {
      await messageDoc.ref.delete();
    }
  } catch (error) {
    console.error("Error deleting associated messages:", error);
    throw new Error("Error deleting associated messages.");
  }
};

const deleteDocumentChunks = async (namespace: string, userId: string ) => {
  try {

    const byteCollection = `users/${userId}/byteStoreCollection`;

    const byteStoreCollectionRef = adminDb.collection(byteCollection);
    const chunksQuerySnapshot = await byteStoreCollectionRef.where('metadata.doc_id', '==', namespace).get();

    if (!chunksQuerySnapshot.empty) {
      const batch = adminDb.batch();
      chunksQuerySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      console.log(`Document chunks for namespace ${namespace} deleted from Firestore.`);
    } else {
      console.warn(`No document chunks found for namespace ${namespace} in Firestore.`);
    }
  } catch (error) {
    console.error('Error deleting document chunks from Firestore:', error);
    throw new Error('Error deleting document chunks from Firestore.');
  }
};

const deleteFileFromStorage = async (namespace: string, userId: string) => {
  try {
    const bucketName = process.env.FIREBASE_STORAGE_BUCKET;
    if (!bucketName) {
      throw new Error('FIREBASE_STORAGE_BUCKET environment variable is not set.');
    }

    const bucket = adminStorage.bucket(bucketName);
    const filePath = `uploads/${userId}/${namespace}`;  // Include user email in the file path

    await bucket.file(filePath).delete();
    console.log(`File ${namespace} deleted from Firebase Storage.`);
  } catch (error) {
    console.error('Error deleting file from Firebase Storage:', error);
    throw new Error('Error deleting file from Firebase Storage.');
  }
};


const addNotification = async (userId: string, message: string) => {
  try {
    const notificationsRef = adminDb.collection('users').doc(userId).collection('notifications');
    await notificationsRef.add({
      message: message,
      timestamp: new Date(),
      read: false
    });
    console.log(`Notification added for user: ${userId} - ${message}`);
  } catch (error) {
    console.error('Error adding notification:', error);
    throw new Error('Error adding notification.');
  }
};