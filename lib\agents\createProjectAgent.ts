/**
 * Create Project Agent
 *
 * This agent automates the creation of projects from Strategic Director Agent outputs by:
 * 1. Analyzing Strategic Director Agent outputs to extract project recommendations
 * 2. Using the existing createProjectTool to create projects with appropriate context
 * 3. Integrating with PMO workflow by updating PMO records with created project IDs
 * 4. Handling both Marketing and PMO contexts appropriately
 * 5. Creating tasks for each project with admin assignment and high priority
 */

import { createProjectTool, CreateProjectInput } from '../tools/createProjectTool';
import { processWithGroq } from '../tools/groq-ai';
import { pmoProjectsTaskAgent, PMOProjectsTaskAgentResult } from './pmoProjectsTaskAgent';
import { adminDb } from '../../components/firebase-admin';
import { z } from 'zod';
import { CalendarTool } from '../tools/calendarTool';

export interface CreateProjectAgentOptions {
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CreateProjectStreamUpdate) => void;
}

export interface CreateProjectStreamUpdate {
  stage: 'analyzing-output' | 'extracting-projects' | 'creating-projects' | 'creating-tasks' | 'updating-pmo' | 'complete';
  data?: any;
  message?: string;
}

export interface CreateProjectAgentResult {
  success: boolean;
  projectsCreated: Array<{
    projectId: string;
    projectName: string;
    description: string;
    tasksCreated: number;
  }>;
  error?: string;
  analysis?: string;
  pmoUpdated?: boolean;
  totalTasksCreated: number;
}

// Schema for extracted project data
const ExtractedProjectSchema = z.object({
  projectName: z.string(),
  projectDescription: z.string(),
  startDate: z.string(), // YYYY-MM-DD format
  endDate: z.string(), // YYYY-MM-DD format
  categories: z.array(z.string()),
  taskInstructions: z.string().optional(),
  priority: z.enum(['Low', 'Medium', 'High']).default('Medium'),
  estimatedDuration: z.string().optional() // e.g., "30 days", "3 months"
});

const ExtractedProjectsResponseSchema = z.object({
  projects: z.array(ExtractedProjectSchema),
  analysis: z.string(),
  confidence: z.number().min(0).max(1)
});

type ExtractedProject = z.infer<typeof ExtractedProjectSchema>;
type ExtractedProjectsResponse = z.infer<typeof ExtractedProjectsResponseSchema>;

export class CreateProjectAgent {
  private options: CreateProjectAgentOptions;
  private calendarTool: CalendarTool;

  constructor(options: CreateProjectAgentOptions = {}) {
    this.options = {
      includeExplanation: true,
      streamResponse: false,
      ...options
    };
    this.calendarTool = new CalendarTool();
  }

  /**
   * Create projects from Strategic Director Agent output
   */
  async createProjectsFromAgentOutput(
    requestId: string,
    userId: string,
    pmoId?: string
  ): Promise<CreateProjectAgentResult> {
    try {
      console.log(`CreateProjectAgent: Starting project creation for requestId: ${requestId}`);

      // Step 1: Retrieve the agent output
      this._streamUpdate('analyzing-output', null, 'Retrieving agent output...');
      const agentOutput = await this._getAgentOutput(requestId);

      if (!agentOutput) {
        throw new Error(`Agent output not found for requestId: ${requestId}`);
      }

      // Step 2: Handle PMO vs general project creation differently
      this._streamUpdate('extracting-projects', null, 'Analyzing output for project recommendations...');
      const pmoTitle = await this._getPMOTitle(agentOutput, pmoId, userId);

      let extractedProjects: ExtractedProjectsResponse;

      if (pmoTitle && pmoId) {
        // PMO workflow: Create project directly from PMO data, skip LLM extraction
        console.log(`CreateProjectAgent: Using PMO workflow for project: ${pmoTitle}`);
        extractedProjects = await this._createProjectFromPMOData(agentOutput, pmoTitle, pmoId, userId);
      } else {
        // General workflow: Extract projects from agent output
        console.log(`CreateProjectAgent: Using general workflow for project extraction`);
        extractedProjects = await this._extractProjectRecommendations(agentOutput);
      }

      if (!extractedProjects.projects || extractedProjects.projects.length === 0) {
        return {
          success: false,
          projectsCreated: [],
          error: 'No project recommendations found in the agent output',
          analysis: extractedProjects.analysis
        };
      }

      // Step 3: Create projects (without tasks initially)
      this._streamUpdate('creating-projects', null, `Creating ${extractedProjects.projects.length} projects...`);
      const createdProjects = await this._createProjects(extractedProjects.projects, userId, agentOutput, pmoId);

      // Step 4: Create tasks for each project using pmoProjectsTaskAgent
      this._streamUpdate('creating-tasks', null, 'Creating tasks from agent output...');
      let totalTasksCreated = 0;

      for (const project of createdProjects) {
        try {
          const taskResult = await pmoProjectsTaskAgent.createTasksFromAgentOutput(
            requestId,
            project.projectId,
            project.projectName,
            project.description
          );

          if (taskResult.success) {
            project.tasksCreated = taskResult.tasksCreated.length;
            totalTasksCreated += taskResult.tasksCreated.length;
            console.log(`CreateProjectAgent: Created ${taskResult.tasksCreated.length} tasks for project ${project.projectName}`);

            // Update project categories to match task categories
            await this._updateProjectCategoriesFromTasks(project.projectId, taskResult.tasksCreated);
          } else {
            console.warn(`CreateProjectAgent: Failed to create tasks for project ${project.projectName}: ${taskResult.error}`);
          }
        } catch (error) {
          console.error(`CreateProjectAgent: Error creating tasks for project ${project.projectName}:`, error);
        }
      }

      // Step 5: Update PMO record if applicable
      let pmoUpdated = false;
      if (pmoId && createdProjects.length > 0) {
        this._streamUpdate('updating-pmo', null, 'Updating PMO record...');
        pmoUpdated = await this._updatePMORecord(pmoId, userId, createdProjects);
      }

      this._streamUpdate('complete', null, 'Project creation completed successfully');

      return {
        success: true,
        projectsCreated: createdProjects,
        analysis: extractedProjects.analysis,
        pmoUpdated,
        totalTasksCreated
      };

    } catch (error) {
      console.error('CreateProjectAgent: Error creating projects:', error);
      return {
        success: false,
        projectsCreated: [],
        error: error instanceof Error ? error.message : String(error),
        totalTasksCreated: 0
      };
    }
  }

  /**
   * Retrieve agent output from Firestore (public for API access)
   */
  async _getAgentOutput(requestId: string): Promise<any> {
    try {
      const doc = await adminDb.collection('Agent_Output').doc(requestId).get();

      if (!doc.exists) {
        throw new Error(`Agent output document not found: ${requestId}`);
      }

      return doc.data();
    } catch (error) {
      console.error('CreateProjectAgent: Error retrieving agent output:', error);
      throw error;
    }
  }

  /**
   * Extract single project from PMO title and agent output (public for API access)
   */
  async _extractSingleProjectFromPMO(agentOutput: any, pmoTitle: string): Promise<ExtractedProjectsResponse> {
    try {
      const outputContent = agentOutput.result?.output || agentOutput.result?.content || '';
      const thinkingContent = agentOutput.result?.thinking || '';
      const agentType = agentOutput.agentType || 'unknown';
      const category = agentOutput.category || agentOutput.pmoMetadata?.category || '';

      // Get current date using Calendar tool
      const currentDateResult = await this.calendarTool.process({
        operation: 'getCurrentDateTime',
        dateFormat: 'yyyy-MM-dd'
      });

      const todayDate = currentDateResult.success ? currentDateResult.result as string : new Date().toISOString().split('T')[0];

      const prompt = `
You are a Project Management AI tasked with creating a SINGLE project from a PMO request and Strategic Director Agent output.

**CURRENT DATE:** ${todayDate}
**PMO Project Title:** ${pmoTitle}
**Agent Output Content:**
${outputContent}

**Agent Thinking Process:**
${thinkingContent}

**Agent Type:** ${agentType}
**Category:** ${category}

**Task:** Create ONE project using the PMO title as the project name, with implementation details from the strategic analysis.

**Requirements:**
1. Use the PMO title "${pmoTitle}" as the project name
2. Create a comprehensive project description based on the strategic analysis
3. Project should be implementable within 30-90 days
4. Include realistic start and end dates based on TODAY'S DATE: ${todayDate}
5. Assign appropriate categories based on the content

**Date Guidelines:**
- Start date should be TODAY (${todayDate}) or a logical future date within 1-7 days
- End date should be calculated based on project scope (typically 30-90 days from start)
- Use YYYY-MM-DD format for all dates
- Consider weekends and realistic project timelines

**Output Format:** Return a JSON object with this exact structure:
{
  "projects": [
    {
      "projectName": "${pmoTitle}",
      "projectDescription": "Comprehensive description based on strategic analysis, including key deliverables and success criteria",
      "startDate": "YYYY-MM-DD (use ${todayDate} or logical start date)",
      "endDate": "YYYY-MM-DD (calculated from start date + estimated duration)",
      "categories": ["Marketing", "Design", "Research", etc.],
      "taskInstructions": "Specific instructions for creating tasks based on the strategic analysis",
      "priority": "High|Medium|Low",
      "estimatedDuration": "X days/weeks/months"
    }
  ],
  "analysis": "Brief explanation of how the strategic content supports the PMO project",
  "confidence": 0.85
}

Always return exactly ONE project in the projects array.
`;

      const response = await processWithGroq({
        prompt,
        model: 'deepseek-r1-distill-llama-70b',
        modelOptions: {
          temperature: 0.3,
          max_tokens: 3000,
        },
      });

      // Parse and validate the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in Groq response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      return ExtractedProjectsResponseSchema.parse(parsedResponse);

    } catch (error) {
      console.error('CreateProjectAgent: Error extracting single project from PMO:', error);
      throw new Error(`Failed to extract project from PMO: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create project directly from PMO data (bypasses LLM extraction for PMO workflows)
   */
  async _createProjectFromPMOData(
    agentOutput: any,
    pmoTitle: string,
    pmoId: string,
    userId: string
  ): Promise<ExtractedProjectsResponse> {
    try {
      console.log(`CreateProjectAgent: Creating project directly from PMO data: ${pmoTitle}`);

      // Get current date using Calendar tool
      const currentDateResult = await this.calendarTool.process({
        operation: 'getCurrentDateTime',
        dateFormat: 'yyyy-MM-dd'
      });

      const todayDate = currentDateResult.success ? currentDateResult.result as string : new Date().toISOString().split('T')[0];

      // Get PMO record to extract additional details
      let pmoDescription = '';
      let pmoCategories: string[] = ['Marketing']; // Default category

      try {
        const pmoDocPath = `users/${userId}/PMO/${pmoId}`;
        const pmoDoc = await adminDb.doc(pmoDocPath).get();
        if (pmoDoc.exists) {
          const pmoData = pmoDoc.data();
          pmoDescription = pmoData?.description || pmoData?.refinedRequestDescription || '';
          if (pmoData?.contextCategories && Array.isArray(pmoData.contextCategories)) {
            pmoCategories = pmoData.contextCategories;
          }
        }
      } catch (error) {
        console.warn('CreateProjectAgent: Could not fetch PMO record for additional details:', error);
      }

      // Calculate project dates (start today, end in 60 days by default)
      const startDate = todayDate;
      const endDateObj = new Date(todayDate);
      endDateObj.setDate(endDateObj.getDate() + 60); // 60 days from start
      const endDate = endDateObj.toISOString().split('T')[0];

      // Create project description from PMO data and strategic analysis
      const outputContent = agentOutput.result?.output || agentOutput.result?.content || '';
      const projectDescription = pmoDescription
        ? `${pmoDescription}\n\nStrategic Analysis: Based on comprehensive strategic analysis, this project will implement the recommendations and action items identified by the Strategic Director team.`
        : `Project created from PMO request: ${pmoTitle}. Strategic Analysis: Based on comprehensive strategic analysis, this project will implement the recommendations and action items identified by the Strategic Director team.`;

      // Return formatted project data (no LLM extraction needed)
      return {
        projects: [
          {
            projectName: pmoTitle,
            projectDescription,
            startDate,
            endDate,
            categories: pmoCategories,
            taskInstructions: `Create tasks based on the strategic analysis provided. Focus on actionable items that support the project objectives: ${pmoTitle}`,
            priority: 'High' as const, // PMO projects are always high priority
            estimatedDuration: '60 days'
          }
        ],
        analysis: `Project created directly from PMO data. Title: ${pmoTitle}. Strategic analysis will be used for task generation.`,
        confidence: 1.0 // High confidence since we're using direct PMO data
      };

    } catch (error) {
      console.error('CreateProjectAgent: Error creating project from PMO data:', error);
      throw new Error(`Failed to create project from PMO data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Extract project recommendations from agent output using AI analysis (legacy method)
   */
  private async _extractProjectRecommendations(agentOutput: any): Promise<ExtractedProjectsResponse> {
    try {
      const outputContent = agentOutput.result?.output || agentOutput.result?.content || '';
      const thinkingContent = agentOutput.result?.thinking || '';
      const agentType = agentOutput.agentType || 'unknown';
      const category = agentOutput.category || agentOutput.pmoMetadata?.category || '';

      // Get current date using Calendar tool
      const currentDateResult = await this.calendarTool.process({
        operation: 'getCurrentDateTime',
        dateFormat: 'yyyy-MM-dd'
      });

      const todayDate = currentDateResult.success ? currentDateResult.result as string : new Date().toISOString().split('T')[0];

      const prompt = `
You are a Project Management AI tasked with analyzing Strategic Director Agent outputs to extract actionable project recommendations.

**CURRENT DATE:** ${todayDate}

AGENT OUTPUT ANALYSIS:
Agent Type: ${agentType}
Category: ${category}
Output Content: ${outputContent}
Strategic Thinking: ${thinkingContent}

TASK: Analyze the above Strategic Director Agent output and extract concrete project recommendations that can be implemented.

EXTRACTION CRITERIA:
1. Look for specific initiatives, campaigns, or strategic actions mentioned
2. Identify clear deliverables and outcomes
3. Extract timeline information or estimate reasonable durations
4. Determine appropriate project categories based on the content
5. Create actionable task instructions for each project

RESPONSE FORMAT: Return a valid JSON object with this exact structure:
{
  "projects": [
    {
      "projectName": "Clear, actionable project name",
      "projectDescription": "Detailed description including objectives and expected outcomes",
      "startDate": "YYYY-MM-DD (use ${todayDate} or logical start date)",
      "endDate": "YYYY-MM-DD (calculated from start date + estimated duration)",
      "categories": ["Marketing", "Design", "Research", etc.],
      "taskInstructions": "Specific instructions for creating tasks based on the strategic analysis",
      "priority": "High|Medium|Low",
      "estimatedDuration": "X days/weeks/months"
    }
  ],
  "analysis": "Brief explanation of how projects were extracted and their strategic alignment",
  "confidence": 0.85
}

GUIDELINES:
- Only extract projects that are clearly actionable and have sufficient detail
- Ensure project names are specific and descriptive
- Categories should align with: Marketing, Design, Research, Sales, Admin, Development, Operations
- Start dates should be TODAY (${todayDate}) or a logical future date within 1-7 days
- End dates should be calculated based on project scope (typically 30-90 days from start)
- Use YYYY-MM-DD format for all dates and consider weekends and realistic project timelines
- Task instructions should be detailed enough for task creation agents
- Confidence should reflect how clear and actionable the extracted projects are

If no clear project recommendations can be extracted, return an empty projects array with appropriate analysis.
`;

      const response = await processWithGroq({
        prompt,
        model: 'deepseek-r1-distill-llama-70b',
        modelOptions: {
          temperature: 0.3,
          max_tokens: 3000,
        },
      });

      // Parse and validate the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in Groq response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      return ExtractedProjectsResponseSchema.parse(parsedResponse);

    } catch (error) {
      console.error('CreateProjectAgent: Error extracting project recommendations:', error);
      throw new Error(`Failed to extract project recommendations: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create projects using basic Firebase operations (without createProjectTool to avoid task conflicts)
   */
  private async _createProjects(
    extractedProjects: ExtractedProject[],
    userId: string,
    agentOutput: any,
    pmoId?: string
  ): Promise<Array<{ projectId: string; projectName: string; description: string; tasksCreated: number }>> {
    const createdProjects = [];

    for (const project of extractedProjects) {
      try {
        console.log(`CreateProjectAgent: Creating project: ${project.projectName}`);

        // Create project data
        const adminUser = '<EMAIL>'; // ADMIN user

        const projectData = {
          name: project.projectName,
          description: project.projectDescription,
          startDate: new Date(project.startDate),
          endDate: new Date(project.endDate),
          owner: adminUser, // FIXED: All projects should be owned by admin
          members: [userId, adminUser], // Include both user and admin
          categories: project.categories,
          status: 'Active',
          createdAt: new Date(),
          updatedAt: new Date(),
          // PMO-specific fields if applicable
          ...(pmoId && {
            pmoId: pmoId,
            pmoCategory: agentOutput.category,
            teamName: this._extractTeamName(agentOutput),
            generatedFromAgentOutput: true,
            agentOutputId: agentOutput.id || 'unknown'
          })
        };

        // Create the project in Firebase
        const projectRef = await adminDb.collection('projects').add(projectData);

        createdProjects.push({
          projectId: projectRef.id,
          projectName: project.projectName,
          description: project.projectDescription,
          tasksCreated: 0 // Will be updated when tasks are created
        });

        console.log(`CreateProjectAgent: Successfully created project ${project.projectName} with ID: ${projectRef.id}`);

      } catch (error) {
        console.error(`CreateProjectAgent: Error creating project ${project.projectName}:`, error);
        // Continue with other projects even if one fails
      }
    }

    return createdProjects;
  }

  /**
   * Update PMO record with created project IDs (array support)
   * Now uses the correct user-specific path: users/{userId}/PMO/{pmoId}
   */
  private async _updatePMORecord(
    pmoId: string,
    userId: string,
    createdProjects: Array<{ projectId: string; projectName: string; description: string; tasksCreated: number }>
  ): Promise<boolean> {
    try {
      const projectIds = createdProjects.map(p => p.projectId);
      const projectSummary = createdProjects.map(p => ({
        id: p.projectId,
        name: p.projectName,
        description: p.description,
        tasksCreated: p.tasksCreated,
        createdAt: new Date().toISOString()
      }));

      // Use the correct user-specific path for PMO records
      const pmoDocPath = `users/${userId}/PMO/${pmoId}`;
      console.log(`CreateProjectAgent: Updating PMO record at path: ${pmoDocPath}`);

      const pmoDoc = await adminDb.doc(pmoDocPath).get();

      if (!pmoDoc.exists) {
        console.error(`CreateProjectAgent: PMO record not found at path: ${pmoDocPath}`);
        return false;
      }

      const existingData = pmoDoc.data() || {};
      const existingProjectIds = existingData.projectIds || [];
      const existingProjects = existingData.generatedProjects || [];

      // Update the PMO record with project information (append to arrays)
      await adminDb.doc(pmoDocPath).update({
        generatedProjects: [...existingProjects, ...projectSummary],
        projectIds: [...existingProjectIds, ...projectIds], // Support multiple projects
        projectGenerationStatus: 'completed',
        projectGenerationDate: new Date(),
        updatedAt: new Date()
      });

      console.log(`CreateProjectAgent: Successfully updated PMO record ${pmoId} with ${projectIds.length} new project IDs`);
      return true;

    } catch (error) {
      console.error('CreateProjectAgent: Error updating PMO record:', error);
      console.error('CreateProjectAgent: PMO ID:', pmoId);
      console.error('CreateProjectAgent: User ID:', userId);
      return false;
    }
  }

  /**
   * Get PMO title from agent output or PMO record (public for API access)
   */
  async _getPMOTitle(agentOutput: any, pmoId?: string, userId?: string): Promise<string | null> {
    try {
      // First try to get from agent output metadata
      if (agentOutput.metadata?.recordTitle) {
        return agentOutput.metadata.recordTitle;
      }

      // Try other metadata fields
      if (agentOutput.metadata?.projectTitle) {
        return agentOutput.metadata.projectTitle;
      }

      if (agentOutput.metadata?.title) {
        return agentOutput.metadata.title;
      }

      // If we have a PMO ID and userId, fetch the PMO record from correct path
      if (pmoId && userId) {
        try {
          const pmoDocPath = `users/${userId}/PMO/${pmoId}`;
          const pmoDoc = await adminDb.doc(pmoDocPath).get();
          if (pmoDoc.exists) {
            const pmoData = pmoDoc.data();
            return pmoData?.title || null;
          }
        } catch (error) {
          console.warn('CreateProjectAgent: Could not fetch PMO record:', error);
        }
      }

      return null;
    } catch (error) {
      console.error('CreateProjectAgent: Error getting PMO title:', error);
      return null;
    }
  }

  /**
   * Update project categories to match the categories from created tasks
   */
  private async _updateProjectCategoriesFromTasks(projectId: string, tasks: any[]): Promise<void> {
    try {
      // Extract unique categories from tasks
      const taskCategories = [...new Set(tasks.map(task => task.category).filter(Boolean))];

      if (taskCategories.length === 0) {
        console.log(`CreateProjectAgent: No task categories found for project ${projectId}`);
        return;
      }

      console.log(`CreateProjectAgent: Updating project ${projectId} categories to match tasks: [${taskCategories.join(', ')}]`);

      // Update the project document with the task categories
      const projectRef = adminDb.collection('projects').doc(projectId);
      await projectRef.update({
        categories: taskCategories,
        updatedAt: new Date()
      });

      console.log(`CreateProjectAgent: Successfully updated project ${projectId} categories`);
    } catch (error) {
      console.error(`CreateProjectAgent: Error updating project categories for ${projectId}:`, error);
    }
  }

  /**
   * Extract team name from agent output context
   */
  private _extractTeamName(agentOutput: any): string {
    if (agentOutput.pmoMetadata?.teamName) {
      return agentOutput.pmoMetadata.teamName;
    }

    if (agentOutput.agentType === 'strategic-director') {
      return 'Marketing';
    }

    if (agentOutput.agentType === 'research-strategic-director' || agentOutput.agentType === 'research-team') {
      return 'Research';
    }

    return 'General';
  }

  /**
   * Stream update helper
   */
  private _streamUpdate(stage: CreateProjectStreamUpdate['stage'], data?: any, message?: string) {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({ stage, data, message });
    }
  }
}

// Export a default instance
export const createProjectAgent = new CreateProjectAgent({
  includeExplanation: true,
  streamResponse: false
});
