// research/ResearchTeamAgent.ts

import { TeamAgent, TeamAgentResult, ResearchTeamAgentOptions, ResearchTeamAgentResult } from '../pmo/TeamAgentInterfaces';
import { Task } from '../../../admin/planner/types';
import { ResearchAgentManager } from './ResearchAgentManager';
import { ResearchTaskRequest } from './ResearchInterfaces';
import { AgenticTeamId } from '../pmo/PMOInterfaces';

/**
 * ResearchTeamAgent - Standardized PMO interface for the Research team
 *
 * This class implements the TeamAgent interface to provide a standardized
 * way for the PMO system to send tasks directly to the Research team.
 * It acts as a bridge between PMO Task format and Research team workflows.
 */
export class ResearchTeamAgent implements TeamAgent {
  private researchManager: ResearchAgentManager;
  private options: ResearchTeamAgentOptions;

  constructor(options: ResearchTeamAgentOptions) {
    this.options = options;
    this.researchManager = new ResearchAgentManager({
      userId: options.userId,
      defaultLlmProvider: 'anthropic',
      defaultLlmModel: 'claude-sonnet-4-20250514'
    });
  }

  /**
   * Process a PMO task using the Research team workflow
   * This is the main entry point for PMO-to-Research team communication
   */
  async processTask(task: Task): Promise<TeamAgentResult> {
    console.log(`[ResearchTeamAgent] Processing PMO task: ${task.title}`);

    try {
      // Initialize research team if not already done
      await this.researchManager.initializeResearchTeam();

      // Stream update: analyzing task
      this.streamUpdate('analyzing-task', { taskId: task.id, title: task.title });

      // Convert PMO Task to Research format
      const researchRequest = this.convertPMOTaskToResearchBrief(task);

      // Stream update: processing task
      this.streamUpdate('processing-task', {
        researchRequest,
        message: 'Converting PMO task to research workflow'
      });

      // Check if this is a PMO strategic task that needs enhanced processing
      const isPMOStrategicTask = this.isPMOStrategicTask(task);

      let result;
      if (isPMOStrategicTask) {
        // Use enhanced PMO research workflow
        result = await this.processPMOStrategicTask(task, researchRequest);
      } else {
        // Use standard research workflow
        result = await this.processStandardResearchTask(researchRequest);
      }

      // Stream update: generating output
      this.streamUpdate('generating-output', {
        result,
        message: 'Finalizing research deliverables'
      });

      // Generate final output and document IDs
      const output = this.generateTaskOutput(result, task);
      const outputDocumentIds = this.extractDocumentIds(result);

      // Stream update: complete
      this.streamUpdate('complete', {
        output: output.substring(0, 200) + '...',
        documentCount: outputDocumentIds.length
      });

      console.log(`[ResearchTeamAgent] Successfully processed task ${task.id}`);

      return {
        success: true,
        taskId: task.id,
        output,
        outputDocumentIds,
        researchSpecificOutput: {
          researchPlanId: result.researchPlanId,
          strategicPlanUrl: result.documentUrl,
          researchMethodology: result.methodology || 'Comprehensive research analysis',
          qualityAssurance: 'Research team QA process completed',
          crossTeamCoordination: result.crossTeamTasks || []
        }
      } as ResearchTeamAgentResult;

    } catch (error: any) {
      console.error(`[ResearchTeamAgent] Error processing task ${task.id}:`, error);

      this.streamUpdate('complete', {
        error: error.message,
        message: 'Task processing failed'
      });

      return {
        success: false,
        taskId: task.id,
        output: '',
        outputDocumentIds: [],
        error: error.message
      };
    }
  }

  /**
   * Convert PMO Task format to ResearchTaskRequest format
   */
  private convertPMOTaskToResearchBrief(task: Task): ResearchTaskRequest {
    // Determine research depth based on task priority and complexity
    let requiredDepth: 'surface' | 'moderate' | 'deep' = 'moderate';

    if (task.priority === 'high' || task.priority === 'critical') {
      requiredDepth = 'deep';
    } else if (task.priority === 'low') {
      requiredDepth = 'surface';
    }

    // Determine output format based on task description
    let outputFormat: 'summary' | 'report' | 'presentation' = 'report';

    const description = task.description.toLowerCase();
    if (description.includes('summary') || description.includes('brief')) {
      outputFormat = 'summary';
    } else if (description.includes('presentation') || description.includes('slides')) {
      outputFormat = 'presentation';
    }

    return {
      taskId: task.id,
      topic: task.title,
      scope: task.description,
      requiredDepth,
      outputFormat,
      deadline: task.dueDate,
      requesterInfo: `PMO Task ${task.id} - ${task.category || 'General'}`
    };
  }

  /**
   * Check if this is a PMO strategic task requiring enhanced processing
   */
  private isPMOStrategicTask(task: Task): boolean {
    const strategicKeywords = [
      'strategic', 'pmo', 'cross-team', 'enterprise', 'initiative',
      'coordination', 'planning', 'assessment', 'analysis'
    ];

    const taskText = `${task.title} ${task.description}`.toLowerCase();
    return strategicKeywords.some(keyword => taskText.includes(keyword));
  }

  /**
   * Process PMO strategic task with enhanced workflow
   */
  private async processPMOStrategicTask(task: Task, researchRequest: ResearchTaskRequest) {
    console.log(`[ResearchTeamAgent] Processing as PMO strategic task`);

    // Extract PMO context from task metadata
    const pmoContext = this.extractPMOContext(task);

    // Use enhanced PMO research workflow
    const result = await this.researchManager.startPMOResearchTask({
      pmoId: pmoContext.pmoId || task.id,
      projectTitle: task.title,
      projectDescription: task.description,
      pmoAssessment: pmoContext.assessment || 'PMO strategic task requiring research analysis',
      teamSelectionRationale: 'Research team selected for comprehensive analysis and strategic insights',
      priority: task.priority?.toUpperCase() || 'MEDIUM',
      category: pmoContext.category || 'Research',
      requirementsDocument: pmoContext.requirementsDocument
    });

    return {
      ...result,
      researchPlanId: result.researchPlanId,
      methodology: 'PMO Strategic Research Workflow',
      crossTeamTasks: [] // Will be populated by strategic task collection
    };
  }

  /**
   * Process standard research task
   */
  private async processStandardResearchTask(researchRequest: ResearchTaskRequest) {
    console.log(`[ResearchTeamAgent] Processing as standard research task`);

    // Start standard research workflow
    const researchPlanId = await this.researchManager.startResearchTask(researchRequest);

    return {
      success: true,
      researchPlanId,
      methodology: 'Standard Research Workflow',
      documentUrl: `research/plan/${researchPlanId}`,
      strategicPlan: 'Standard research plan created'
    };
  }

  /**
   * Extract PMO context from task metadata
   */
  private extractPMOContext(task: Task): {
    pmoId?: string;
    assessment?: string;
    category?: string;
    requirementsDocument?: string;
  } {
    const metadata = task.metadata || {};

    return {
      pmoId: metadata.pmoId || metadata.source === 'PMO' ? task.id : undefined,
      assessment: metadata.pmoAssessment || metadata.assessment,
      category: metadata.category || task.category,
      requirementsDocument: metadata.requirementsDocument
    };
  }

  /**
   * Generate comprehensive task output
   */
  private generateTaskOutput(result: any, task: Task): string {
    const timestamp = new Date().toISOString();

    return `# Research Team Task Completion Report

## Task Information
- **Task ID**: ${task.id}
- **Title**: ${task.title}
- **Completed**: ${timestamp}
- **Research Team**: Enhanced Research Lead Agent

## Research Execution Summary
${result.strategicPlan ? `
### Strategic Implementation Plan
A comprehensive strategic implementation plan has been created and is available at: ${result.documentUrl}

### Key Research Components
- **Research Plan ID**: ${result.researchPlanId}
- **Methodology**: ${result.methodology}
- **Quality Assurance**: Research team QA process completed
` : `
### Research Analysis
- **Research Plan ID**: ${result.researchPlanId}
- **Methodology**: ${result.methodology}
- **Status**: Research workflow initiated and executed
`}

## Deliverables
${result.documentTitle ? `- Strategic Implementation Plan: "${result.documentTitle}"` : ''}
- Research Analysis Report
- Quality Assurance Review
- Cross-team Coordination Plan (if applicable)

## Research Team Capabilities Applied
- Information retrieval and analysis
- Data synthesis and insights generation
- Strategic planning and recommendations
- PMO compliance and standards adherence
- Cross-functional coordination

## Next Steps
The research deliverables are ready for PMO review and stakeholder distribution. All outputs follow PMO standards and include proper documentation and citations.

---
*Generated by Research Team Agent - PMO Integration*
*Research Plan: ${result.researchPlanId}*
${result.documentUrl ? `*Strategic Plan: ${result.documentUrl}*` : ''}`;
  }

  /**
   * Extract document IDs from research results
   */
  private extractDocumentIds(result: any): string[] {
    const documentIds: string[] = [];

    if (result.researchPlanId) {
      documentIds.push(result.researchPlanId);
    }

    if (result.documentUrl) {
      // Extract document ID from URL
      const urlParts = result.documentUrl.split('/');
      const docId = urlParts[urlParts.length - 1];
      if (docId && docId !== result.researchPlanId) {
        documentIds.push(docId);
      }
    }

    // Add any additional document IDs from strategic task collections
    if (result.crossTeamTasks && Array.isArray(result.crossTeamTasks)) {
      result.crossTeamTasks.forEach((task: any) => {
        if (task.documentId) {
          documentIds.push(task.documentId);
        }
      });
    }

    return documentIds;
  }

  /**
   * Send stream update if streaming is enabled
   */
  private streamUpdate(stage: 'analyzing-task' | 'processing-task' | 'generating-output' | 'complete', data?: any) {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({
        stage,
        data,
        message: data?.message || `Research team ${stage.replace('-', ' ')}`
      });
    }
  }
}
