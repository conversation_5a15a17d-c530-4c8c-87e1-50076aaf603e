/**
 * PMO-to-Research Team Workflow Test
 * 
 * This file demonstrates the complete "send to Research team" workflow
 * from PMO task creation to Research team delivery of results.
 */

import { ResearchTeamAgent } from './ResearchTeamAgent';
import { ResearchAgentManager } from './ResearchAgentManager';
import { PMOTaskConverter } from './PMOTaskConverter';
import { Task } from '../../../admin/planner/types';
import { AgenticTeamId } from '../pmo/PMOInterfaces';

/**
 * Test the complete PMO-to-Research workflow
 */
export async function testPMOToResearchWorkflow() {
  console.log('=== PMO-to-Research Team Workflow Test ===\n');

  try {
    // Step 1: Simulate PMO task creation
    console.log('📋 Step 1: Creating PMO task for Research team...');
    const pmoTask = createSamplePMOTask();
    console.log('✅ PMO task created:', {
      id: pmoTask.id,
      title: pmoTask.title,
      priority: pmoTask.priority,
      assignedTo: pmoTask.assignedTo
    });

    // Step 2: Validate task for research processing
    console.log('\n🔍 Step 2: Validating PMO task for Research team...');
    const validation = PMOTaskConverter.validatePMOTaskForResearch(pmoTask);
    console.log('Validation result:', {
      valid: validation.valid,
      errors: validation.errors.length,
      warnings: validation.warnings.length
    });

    if (!validation.valid) {
      console.error('❌ Task validation failed:', validation.errors);
      return false;
    }

    // Step 3: Convert PMO task to research format
    console.log('\n🔄 Step 3: Converting PMO task to Research format...');
    const researchBrief = PMOTaskConverter.convertPMOTaskToResearchBrief(pmoTask);
    const enhancedBrief = PMOTaskConverter.convertPMOTaskToEnhancedResearchBrief(pmoTask);
    console.log('✅ Task conversion completed:', {
      researchTaskId: researchBrief.taskId,
      topic: researchBrief.topic,
      requiredDepth: researchBrief.requiredDepth,
      outputFormat: researchBrief.outputFormat,
      hasPMOContext: !!enhancedBrief.pmoId
    });

    // Step 4: Process task using ResearchTeamAgent
    console.log('\n🔬 Step 4: Processing task with ResearchTeamAgent...');
    const researchTeamAgent = new ResearchTeamAgent({
      userId: 'test-user-123',
      includeExplanation: true,
      streamResponse: true,
      onStreamUpdate: (update) => {
        console.log(`   📡 Stream: ${update.stage} - ${update.message}`);
      }
    });

    const result = await researchTeamAgent.processTask(pmoTask);
    console.log('✅ Research team processing completed:', {
      success: result.success,
      taskId: result.taskId,
      outputLength: result.output.length,
      documentCount: result.outputDocumentIds.length,
      hasResearchSpecificOutput: !!(result as any).researchSpecificOutput
    });

    // Step 5: Generate PMO task summary
    console.log('\n📊 Step 5: Generating PMO task summary...');
    const taskSummary = PMOTaskConverter.generatePMOTaskSummary(pmoTask, result);
    console.log('✅ PMO task summary generated (length:', taskSummary.length, 'characters)');

    // Step 6: Convert results back to PMO format
    console.log('\n🔄 Step 6: Converting results back to PMO task format...');
    const taskUpdate = PMOTaskConverter.convertResearchResultToPMOTaskUpdate(pmoTask, result);
    console.log('✅ PMO task update prepared:', {
      status: taskUpdate.status,
      hasMetadata: !!taskUpdate.metadata,
      researchPlanId: taskUpdate.metadata?.researchPlanId
    });

    console.log('\n🎉 PMO-to-Research workflow test completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ PMO-to-Research workflow test failed:', error);
    return false;
  }
}

/**
 * Test ResearchTeamAgent with different task types
 */
export async function testResearchTeamAgentTaskTypes() {
  console.log('\n=== ResearchTeamAgent Task Types Test ===\n');

  const taskTypes = [
    createStandardResearchTask(),
    createPMOStrategicTask(),
    createCrossTeamTask(),
    createUrgentResearchTask()
  ];

  const researchAgent = new ResearchTeamAgent({
    userId: 'test-user-123',
    includeExplanation: true,
    streamResponse: false
  });

  for (const [index, task] of taskTypes.entries()) {
    console.log(`\n📋 Testing task type ${index + 1}: ${task.title}`);
    
    try {
      const result = await researchAgent.processTask(task);
      console.log('✅ Task processed successfully:', {
        success: result.success,
        methodology: (result as any).researchSpecificOutput?.researchMethodology,
        documentCount: result.outputDocumentIds.length
      });
    } catch (error) {
      console.error('❌ Task processing failed:', error);
    }
  }

  console.log('\n✅ ResearchTeamAgent task types test completed!');
}

/**
 * Test PMO API integration simulation
 */
export async function testPMOAPIIntegration() {
  console.log('\n=== PMO API Integration Test ===\n');

  // Simulate PMO notify team API call
  console.log('📤 Simulating PMO notify team API call...');
  
  const pmoNotificationData = {
    pmoId: 'PMO-2024-003',
    teamId: AgenticTeamId.Research,
    teamName: 'Research',
    projectTitle: 'Market Analysis for AI Product Launch',
    projectDescription: 'Comprehensive market research and competitive analysis for new AI product introduction',
    pmoAssessment: 'High-priority strategic initiative requiring deep market insights and competitive intelligence',
    teamSelectionRationale: 'Research team selected for expertise in market analysis and data synthesis',
    priority: 'HIGH',
    category: 'Market Intelligence',
    userId: 'test-user-123'
  };

  // Simulate research collaboration API body
  const collaborationBody = {
    prompt: pmoNotificationData.projectDescription,
    modelProvider: 'anthropic',
    modelName: 'claude-3-5-sonnet-20241022',
    userId: pmoNotificationData.userId,
    context: `PMO Research Requirements for ${pmoNotificationData.projectTitle}`,
    category: pmoNotificationData.category.toLowerCase(),
    metadata: {
      source: 'PMO',
      pmoId: pmoNotificationData.pmoId,
      projectTitle: pmoNotificationData.projectTitle,
      autoTriggered: true,
      triggerTimestamp: new Date().toISOString(),
      pmoAssessment: pmoNotificationData.pmoAssessment,
      teamSelectionRationale: pmoNotificationData.teamSelectionRationale,
      priority: pmoNotificationData.priority,
      taskId: `pmo-research-${pmoNotificationData.pmoId}-${Date.now()}`
    }
  };

  console.log('✅ PMO API integration data prepared:', {
    pmoId: pmoNotificationData.pmoId,
    teamId: pmoNotificationData.teamId,
    hasMetadata: !!collaborationBody.metadata,
    autoTriggered: collaborationBody.metadata.autoTriggered
  });

  console.log('\n📊 Research collaboration API would be called with:');
  console.log('- Endpoint: /api/research-agent-collaboration');
  console.log('- Method: POST');
  console.log('- PMO Context: Included');
  console.log('- Auto-triggered: Yes');

  console.log('\n✅ PMO API integration test completed!');
}

/**
 * Create sample PMO task for testing
 */
function createSamplePMOTask(): Task {
  return {
    id: 'pmo-task-001',
    title: 'Market Research for Product Launch Strategy',
    description: 'Conduct comprehensive market research to support strategic product launch planning, including competitive analysis, market sizing, and customer segmentation.',
    category: 'Market Intelligence',
    priority: 'high',
    status: 'assigned',
    assignedTo: AgenticTeamId.Research,
    createdAt: new Date(),
    updatedAt: new Date(),
    dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
    metadata: {
      source: 'PMO',
      pmoId: 'PMO-2024-001',
      pmoAssessment: 'Strategic initiative requiring comprehensive market analysis and competitive intelligence',
      teamSelectionRationale: 'Research team selected for expertise in market analysis and data synthesis',
      requirementsDocument: 'req-doc-001',
      strategic: true,
      crossTeamCoordination: true
    }
  };
}

/**
 * Create different task types for testing
 */
function createStandardResearchTask(): Task {
  return {
    id: 'research-task-001',
    title: 'Customer Satisfaction Analysis',
    description: 'Analyze customer satisfaction data and identify improvement opportunities.',
    category: 'Customer Intelligence',
    priority: 'medium',
    status: 'assigned',
    assignedTo: AgenticTeamId.Research,
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: { source: 'Direct Request' }
  };
}

function createPMOStrategicTask(): Task {
  return {
    id: 'pmo-strategic-001',
    title: 'Strategic Market Positioning Analysis',
    description: 'PMO strategic initiative to analyze market positioning and competitive landscape for enterprise expansion.',
    category: 'Strategic Planning',
    priority: 'critical',
    status: 'assigned',
    assignedTo: AgenticTeamId.Research,
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      source: 'PMO',
      pmoId: 'PMO-2024-002',
      strategic: true,
      pmoAssessment: 'Critical strategic analysis for enterprise expansion'
    }
  };
}

function createCrossTeamTask(): Task {
  return {
    id: 'cross-team-001',
    title: 'Cross-functional Product Research Initiative',
    description: 'Research initiative requiring coordination with marketing, sales, and business analysis teams for comprehensive product strategy.',
    category: 'Product Analysis',
    priority: 'high',
    status: 'assigned',
    assignedTo: AgenticTeamId.Research,
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      crossTeamCoordination: true,
      requiredTeams: ['Marketing', 'Sales', 'Business Analysis']
    }
  };
}

function createUrgentResearchTask(): Task {
  return {
    id: 'urgent-001',
    title: 'Urgent Competitive Intelligence Report',
    description: 'Urgent analysis of competitor product launch and market impact assessment.',
    category: 'Market Intelligence',
    priority: 'critical',
    status: 'assigned',
    assignedTo: AgenticTeamId.Research,
    createdAt: new Date(),
    updatedAt: new Date(),
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    metadata: {
      urgent: true,
      outputFormat: 'summary'
    }
  };
}

/**
 * Run all PMO-Research workflow tests
 */
export async function runAllPMOResearchTests() {
  console.log('🚀 Starting PMO-to-Research Team Workflow Tests...\n');

  const results = await Promise.allSettled([
    testPMOToResearchWorkflow(),
    testResearchTeamAgentTaskTypes(),
    testPMOAPIIntegration()
  ]);

  const successCount = results.filter(result => 
    result.status === 'fulfilled' && result.value === true
  ).length;

  console.log(`\n📊 Test Results: ${successCount}/${results.length} tests passed`);
  
  if (successCount === results.length) {
    console.log('🎉 All PMO-Research workflow tests passed! Implementation successful.');
  } else {
    console.log('⚠️  Some tests failed. Please review the implementation.');
  }

  return successCount === results.length;
}

// Export individual test functions
export {
  testPMOToResearchWorkflow,
  testResearchTeamAgentTaskTypes,
  testPMOAPIIntegration
};
